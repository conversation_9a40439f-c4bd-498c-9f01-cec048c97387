import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { PaymentStatus, PaymentMethod } from '@/lib/types/prisma';
import jwt from 'jsonwebtoken';
import { logTransaction } from '@/lib/logger';
// import { v4 as uuidv4 } from 'uuid'; // <-- Hapus impor uuid
// import { cookies } from 'next/headers'; // Hapus impor ini
// import { getAuth } from '@clerk/nextjs/server'; // Hapus atau ganti impor ini
// import { getServerSession } from "next-auth/next" // Contoh: Menggunakan NextAuth.js
// import { authOptions } from "../auth/[...nextauth]/route" // Contoh: Path ke authOptions

// Definisikan tipe untuk payload JWT Anda (sesuaikan)
interface JwtPayload {
  id: string; // <-- U<PERSON> ke 'id' sesuai /api/auth/me
  // tambahkan field lain jika ada (name, email, role, iat, exp, dll.)
  [key: string]: unknown;
}

// Fungsi helper untuk parsing cookie dari header string
function getCookieValue(cookieHeader: string | null, cookieName: string): string | null {
  if (!cookieHeader) return null;
  const cookies = cookieHeader.split('; ');
  for (const cookie of cookies) {
    const [name, value] = cookie.split('=');
    if (name === cookieName) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

// GET semua transaksi
export async function GET(request: NextRequest) {
  console.log("--- GET /api/transactions --- START ---"); // Log Start
  try {
    const { searchParams } = new URL(request.url);
    const paymentStatusParam = searchParams.get('paymentStatus') || searchParams.get('status');
    const outletId = searchParams.get('outletId');
    const paymentMethodParam = searchParams.get('paymentMethod');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = searchParams.get('limit');
    const page = searchParams.get('page') || '1';
    const therapistId = searchParams.get('therapistId');
    const customerId = searchParams.get('customerId');
    console.log(`Received therapistId: ${therapistId}, customerId: ${customerId}`); // Log therapistId dan customerId

    const where: any = {};

    // Filter berdasarkan paymentStatus
    if (paymentStatusParam) {
      const upperStatus = paymentStatusParam.toUpperCase();
      if (Object.values(PaymentStatus).includes(upperStatus as PaymentStatus)) {
         where.paymentStatus = upperStatus as PaymentStatus;
      } else {
         console.warn(`Invalid paymentStatus parameter received: ${paymentStatusParam}`);
      }
    }

    // Filter berdasarkan paymentMethod
    if (paymentMethodParam) {
      const upperMethod = paymentMethodParam.toUpperCase();
      if (Object.values(PaymentMethod).includes(upperMethod as PaymentMethod)) {
         where.paymentMethod = upperMethod as PaymentMethod;
      } else {
         console.warn(`Invalid paymentMethod parameter received: ${paymentMethodParam}`);
      }
    }

    // Filter berdasarkan tanggal createdAt
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) { where.createdAt.gte = new Date(startDate + 'T00:00:00Z'); }
      if (endDate) { where.createdAt.lte = new Date(endDate + 'T23:59:59Z'); }
    }

    // Filter berdasarkan outletId
    if (outletId) { where.outletId = outletId; }

    // Filter berdasarkan therapistId
    if (therapistId) { where.therapistId = therapistId; }

    // Filter berdasarkan customerId
    if (customerId) { where.customerId = customerId; }

    console.log("Final 'where' clause:", JSON.stringify(where, null, 2)); // Log final where clause

    // 1. Hitung total transaksi
    console.log("Counting transactions...");
    const totalTransactions = await prisma.transaction.count({ where });
    console.log(`Transaction count: ${totalTransactions}`);

    // 2. Hitung total revenue
    const revenueWhere: any = {
      ...where,
      paymentStatus: PaymentStatus.PAID
    };
    console.log("Aggregating revenue...");
    const aggregateResult = await prisma.transaction.aggregate({ _sum: { totalAmount: true }, where: revenueWhere });
    const totalRevenue = aggregateResult._sum?.totalAmount ?? 0;
    console.log(`Total revenue: ${totalRevenue}`);

    // Set pagination
    const pageSize = limit ? parseInt(limit) : (totalTransactions > 0 ? totalTransactions : 20);
    const pageNumber = parseInt(page);
    const skip = (pageNumber - 1) * pageSize;

    // 3. Ambil data transaksi dari DB
    console.log("Finding transactions...");
    const dbTransactions = await prisma.transaction.findMany({
      where,
      select: {
        id: true,
        displayId: true,
        createdAt: true,
        totalAmount: true,
        therapistCommissionEarned: true,
        paymentMethod: true,
        notes: true,
        customer: { select: { id: true, name: true, phone: true } },
        therapist: { select: { id: true, name: true } },
        outlet: { select: { id: true, name: true, address: true, phone: true } },
        booking: {
            select: {
                bookingServices: {
                    select: {
                        service: { select: { name: true, price: true } }
                    }
                }
            }
        },
        transactionItems: {
           select: {
               quantity: true,
               price: true,
               service: { select: { name: true } }
           }
        },
        createdBy: {
          select: {
            id: true,
            name: true
          }
        },
        splitPayment: true // Include split payment data
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: pageSize
    });
    console.log(`Found ${dbTransactions.length} transactions in DB.`);

    // 4. Transformasi data sebelum dikirim ke frontend
    const transactions = dbTransactions.map(trx => {
      type BookingServiceItem = { service: { name: string; price: number } };
      type TransactionItemData = {
         quantity: number;
         price: number;
         service: { name: string };
      };

      let itemsData: { name: string; price: number; quantity: number }[] = [];
      // Selalu prioritaskan transactionItems daripada bookingServices
      if (trx.transactionItems && trx.transactionItems.length > 0) {
         console.log(`Transaction ${trx.id}: Using transactionItems`);
         itemsData = trx.transactionItems.map((ti: TransactionItemData) => ({
             name: ti.service.name,
             price: ti.price,
             quantity: ti.quantity
           }));
      } else if (trx.booking?.bookingServices && trx.booking.bookingServices.length > 0) {
         console.log(`Transaction ${trx.id}: Using bookingServices as fallback`);
         itemsData = trx.booking.bookingServices.map((bs: BookingServiceItem) => ({
             name: bs.service.name,
             price: bs.service.price,
             quantity: 1
           }));
      } else {
         console.log(`Transaction ${trx.id}: No items found in transactionItems or bookingServices`);
      }

      return {
        id: trx.id,
        transactionId: trx.displayId || String(trx.id),
        createdAt: trx.createdAt,
        outletName: trx.outlet?.name,
        customer: trx.customer,
        therapistName: trx.therapist?.name,
        therapist: trx.therapist,
        outlet: trx.outlet,
        items: itemsData,
        totalAmount: trx.totalAmount,
        total: trx.totalAmount,
        paymentMethod: trx.paymentMethod,
        note: trx.notes,
        therapistCommissionEarned: trx.therapistCommissionEarned ?? 0,
        createdBy: trx.createdBy,
        splitPayment: trx.splitPayment // Include split payment data
      };
    });
    console.log("Data transformed.");

    // Kembalikan data
    console.log("--- GET /api/transactions --- SUCCESS ---"); // Log Success
    return NextResponse.json({
      message: 'Data transaksi berhasil diambil',
      transactions,
      totalRevenue,
      pagination: {
        total: totalTransactions,
        page: pageNumber,
        pageSize,
        pageCount: pageSize > 0 ? Math.ceil(totalTransactions / pageSize) : 0
      }
    });
  } catch (error: unknown) {
    console.error("--- GET /api/transactions --- ERROR ---", error); // Log Error

    // Tambahkan logging lebih detail untuk debugging di Vercel
    if (error instanceof Error) {
      console.error("Error name:", error.name);
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }

    // Cek apakah error dari Prisma
    if (error instanceof Error && error.name === 'PrismaClientKnownRequestError') {
      const prismaError = error as any;
      console.error("Prisma error code:", prismaError.code);
      console.error("Prisma error meta:", prismaError.meta);
    }

    const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data transaksi';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// POST
export async function POST(request: NextRequest) {
  console.log("API /api/transactions POST request received."); // Log awal
  try {
    // --- Mendapatkan User ID dari Cookie JWT (via Header) ---
    let createdById: string | null | undefined = null;
    const cookieHeader = request.headers.get('cookie'); // <-- Baca header 'cookie'
    const token = getCookieValue(cookieHeader, 'user_token'); // <-- Gunakan helper untuk parse

    if (token) {
      console.log("Token found in 'user_token' cookie (from header).");
      try {
        if (!process.env.JWT_SECRET) {
          throw new Error('JWT_SECRET environment variable is not set.');
        }
        // Verifikasi token dan decode payload
        const decoded = jwt.verify(token, process.env.JWT_SECRET) as JwtPayload;
        // Pastikan field 'id' ada di payload (sesuai /api/auth/me)
        if (decoded && decoded.id) {
          createdById = decoded.id; // <-- Gunakan decoded.id
          console.log("JWT verified successfully. User ID:", createdById);
        } else {
          console.warn("JWT payload does not contain id.");
        }
      } catch (error: any) {
        console.error('JWT Verification Error:', error.message);
      }
    } else {
      console.warn("'user_token' cookie not found in header.");
    }
    // --- Akhir Bagian Otentikasi ---

    // Pastikan createdById berhasil didapatkan setelah logika otentikasi Anda
    if (!createdById) {
       console.error("User ID could not be determined after auth check (cookie).");
       return NextResponse.json({ error: 'Otentikasi pengguna gagal. Token tidak valid atau tidak ditemukan.' }, { status: 401 });
    }
    // --------------------------

    const body = await request.json();
    console.log("API RECEIVED BODY FOR COMMISSION CALC:", body);

    // Log khusus untuk debugging masalah layanan
    if (body.items && Array.isArray(body.items)) {
      console.log(`RECEIVED ${body.items.length} ITEMS FROM FRONTEND:`, JSON.stringify(body.items, null, 2));
    } else {
      console.log("NO ITEMS RECEIVED FROM FRONTEND");
    }

    if (body.bookingId) {
      console.log(`TRANSACTION ASSOCIATED WITH BOOKING ID: ${body.bookingId}`);
    }

    const {
      bookingId = '',
      totalAmount,
      paymentStatus = 'PENDING',
      paymentMethod,
      notes,
      outletId,
      customerId,
      therapistId,
      items, // <-- Asumsikan ada 'items' untuk walk-in: { serviceId: string; quantity: number }[]
      splitPayment, // <-- Data split bill: { firstMethod, secondMethod, cashAmount, changeAmount }
      // Tambahkan field untuk diskon dan biaya tambahan
      discountType,
      discountValue,
      discountAmount,
      additionalCharge
    } = body;

    // Perbaiki validasi input
    // Hapus pengecekan !bookingId karena string kosong diizinkan
    if (typeof totalAmount !== 'number' || !outletId || !customerId || !therapistId || !createdById) {
      console.error("Validation failed - Missing core data", { totalAmount, outletId, customerId, therapistId, createdById });
      return NextResponse.json(
        { error: 'Data tidak lengkap: totalAmount, outletId, customerId, therapistId, dan createdById diperlukan' }, // Hapus bookingId dari pesan error wajib
        { status: 400 }
      );
    }

    // Validasi PaymentStatus dan PaymentMethod (gunakan enum dari Prisma)
    const validPaymentStatuses = ['PENDING', 'PAID', 'REFUNDED', 'FAILED'];
    const validPaymentMethods = ['CASH', 'CREDIT_CARD', 'DEBIT_CARD', 'TRANSFER', 'DIGITAL_WALLET', 'QRIS', 'SPLIT'];

    const finalPaymentStatus = paymentStatus.toUpperCase();
    const finalPaymentMethod = paymentMethod ? paymentMethod.toUpperCase() : null;

    if (!validPaymentStatuses.includes(finalPaymentStatus)) {
        console.error("Validation failed - Invalid Payment Status", { finalPaymentStatus });
        return NextResponse.json({ error: `Payment Status tidak valid: ${paymentStatus}` }, { status: 400 });
    }
    if (finalPaymentMethod && !validPaymentMethods.includes(finalPaymentMethod)) {
        console.error("Validation failed - Invalid Payment Method", { finalPaymentMethod });
        return NextResponse.json({ error: `Metode pembayaran tidak valid: ${paymentMethod}` }, { status: 400 });
    }

    // Validasi data split payment jika metode pembayaran adalah SPLIT
    if (finalPaymentMethod === 'SPLIT') {
        if (!splitPayment) {
            console.error("Validation failed - Split payment data missing");
            return NextResponse.json({ error: 'Data split bill diperlukan untuk metode pembayaran SPLIT' }, { status: 400 });
        }

        const { firstMethod, secondMethod } = splitPayment;
        if (!firstMethod || !secondMethod) {
            console.error("Validation failed - Split payment methods missing");
            return NextResponse.json({ error: 'Metode pembayaran pertama dan kedua diperlukan untuk split bill' }, { status: 400 });
        }

        const firstMethodUpper = firstMethod.toUpperCase();
        const secondMethodUpper = secondMethod.toUpperCase();

        if (!validPaymentMethods.filter(m => m !== 'SPLIT').includes(firstMethodUpper) ||
            !validPaymentMethods.filter(m => m !== 'SPLIT').includes(secondMethodUpper)) {
            console.error("Validation failed - Invalid split payment methods", { firstMethodUpper, secondMethodUpper });
            return NextResponse.json({ error: 'Metode pembayaran split bill tidak valid' }, { status: 400 });
        }
    }

    let calculatedCommission = 0;
    let servicesDetails: { id: string, commission: number, quantity: number }[] = [];

    // --- Logika Pengambilan Detail Layanan & Komisi ---
    if (bookingId && typeof bookingId === 'string' && bookingId.trim() !== '') {
      console.log(`Calculating commission based on bookingId: ${bookingId}`);
      const bookingWithServices = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          bookingServices: {
            include: {
              service: { select: { id: true, commission: true, price: true } }
            }
          }
        }
      });

      if (!bookingWithServices) {
        return NextResponse.json({ error: 'Booking tidak ditemukan untuk perhitungan komisi' }, { status: 404 });
      }
      // Cek outlet mismatch (jika perlu)
      // ...

      // Ambil detail dari booking
      servicesDetails = bookingWithServices.bookingServices.map(bs => ({
        id: bs.service.id,
        commission: bs.service.commission ?? 0,
        price: bs.price || bs.service.price,
        quantity: bs.quantity || 1 // Gunakan quantity dari BookingService jika ada
      }));

      // Tambahan: Cek komisi khusus terapis untuk layanan-layanan di booking
      console.log(`Checking for special therapist commissions for therapist ${therapistId}`);
      const serviceIds = servicesDetails.map(s => s.id);

      const therapistSpecialCommissions = await prisma.therapistServiceCommission.findMany({
        where: {
          therapistId: therapistId,
          serviceId: { in: serviceIds }
        }
      });

      console.log(`Found ${therapistSpecialCommissions.length} special commissions`);

      // Buat map untuk komisi khusus terapis
      const specialCommissionMap = new Map();
      therapistSpecialCommissions.forEach(sc => {
        specialCommissionMap.set(sc.serviceId, sc.commission);
        console.log(`Special commission for service ${sc.serviceId}: ${sc.commission}`);
      });

      // Update servicesDetails dengan komisi khusus jika ada
      servicesDetails = servicesDetails.map(service => {
        // Cek apakah ada komisi khusus untuk terapis ini
        const hasSpecialCommission = specialCommissionMap.has(service.id);
        const finalCommission = hasSpecialCommission
          ? specialCommissionMap.get(service.id)
          : service.commission;

        console.log(`Final commission for service ${service.id}: ${finalCommission} (special: ${hasSpecialCommission})`);

        return {
          ...service,
          commission: finalCommission
        };
      });

    } else if (items && Array.isArray(items) && items.length > 0) {
      console.log("Calculating commission based on walk-in items:", items);
      const serviceIds = items.map(item => item.serviceId).filter(id => id);
      if (serviceIds.length === 0) {
         return NextResponse.json({ error: 'Tidak ada serviceId valid dalam items untuk walk-in' }, { status: 400 });
      }

      const servicesFromDb = await prisma.service.findMany({
        where: {
          id: { in: serviceIds },
          outlets: {
            some: {
              outletId: outletId
            }
          }
        },
        select: { id: true, commission: true, price: true }
      });

      // Cari komisi khusus untuk terapis ini
      const therapistSpecialCommissions = await prisma.therapistServiceCommission.findMany({
        where: {
          therapistId: therapistId,
          serviceId: { in: serviceIds }
        }
      });

      // Buat map untuk komisi khusus terapis
      const specialCommissionMap = new Map();
      therapistSpecialCommissions.forEach(sc => {
        specialCommissionMap.set(sc.serviceId, sc.commission);
      });

      const serviceMap = new Map();
      servicesFromDb.forEach(s => {
        serviceMap.set(s.id, {
          commission: s.commission,
          price: s.price
        });
      });

      servicesDetails = items.map(item => {
        const serviceInfo = serviceMap.get(item.serviceId);
        // Cek apakah ada komisi khusus untuk terapis ini
        const hasSpecialCommission = specialCommissionMap.has(item.serviceId);
        let commission = hasSpecialCommission
          ? specialCommissionMap.get(item.serviceId)
          : (serviceInfo?.commission ?? 0);

        // Pastikan komisi adalah nilai absolut, bukan persentase
        const servicePrice = serviceInfo?.price ?? 0;

        console.log(`Final commission for service ${item.serviceId}: ${commission} (special: ${hasSpecialCommission})`);

        return {
          id: item.serviceId,
          commission: commission,
          price: servicePrice,
          quantity: item.quantity > 0 ? item.quantity : 1
        };
      });

    } else {
      console.warn("Cannot calculate commission: No bookingId or valid items provided.");
      // Bisa return error 400 atau biarkan komisi 0 tergantung kebutuhan
      // return NextResponse.json({ error: 'Data layanan tidak ditemukan untuk perhitungan komisi (perlu bookingId atau items)' }, { status: 400 });
    }
    // --- Akhir Logika Pengambilan Detail Layanan ---

    // Implementasi perhitungan komisi
    // Hitung komisi dasar dari layanan
    calculatedCommission = servicesDetails.reduce((total, service) => {
      // Pastikan commission adalah nilai absolut, bukan persentase
      let commissionValue = service.commission;

      // Debug untuk melihat nilai komisi yang digunakan
      console.log(`Service ${service.id} commission value: ${commissionValue}, type: ${typeof commissionValue}`);

      // Hitung komisi per item dengan quantity
      const itemCommission = commissionValue * service.quantity;
      console.log(`Adding to commission: ${commissionValue} x ${service.quantity} = ${itemCommission}`);

      return total + itemCommission;
    }, 0);

    // Jika ada biaya tambahan, tambahkan ke komisi terapis
    // Biaya tambahan dimasukkan ke dalam perhitungan pendapatan terapis
    if (additionalCharge && additionalCharge > 0) {
      console.log(`Adding additional charge to therapist commission: ${additionalCharge}`);
      calculatedCommission += additionalCharge;
    }

    // Diskon tidak mempengaruhi pendapatan terapis, jadi tidak perlu dikurangkan dari komisi
    console.log(`Final calculated commission for transaction (including additional charge): ${calculatedCommission}`);

    let bookingData = null;
    // --- Logika terkait Booking (hanya jika bookingId valid) ---
    if (bookingId && typeof bookingId === 'string' && bookingId.trim() !== '') {
        console.log(`Valid bookingId detected: ${bookingId}. Checking booking...`);
        // Cek booking dan outletId
        bookingData = await prisma.booking.findUnique({
           where: { id: bookingId },
           include: {
               bookingServices: {
                   select: {
                       service: {
                           select: {
                               id: true,
                               outlets: {
                                   select: {
                                       outletId: true
                                   }
                               }
                           }
                       }
                   }
               }
           }
        });

        if (!bookingData) {
             console.error(`Booking with ID ${bookingId} not found.`);
             return NextResponse.json({ error: 'Booking tidak ditemukan' }, { status: 404 });
         }
        // PENTING: Cek ini mungkin tidak relevan jika 1 booking bisa banyak service dari outlet berbeda?
        // Sesuaikan logika ini jika perlu.
        const serviceOutlets = bookingData.bookingServices?.[0]?.service?.outlets || [];
        const serviceHasOutlet = serviceOutlets.some(outlet => outlet.outletId === outletId);

        if (!serviceHasOutlet) {
             console.error(`Booking outlet mismatch. Service outlets: ${JSON.stringify(serviceOutlets.map(o => o.outletId))}, Transaction outlet: ${outletId}`);
             return NextResponse.json({ error: 'Booking tidak sesuai dengan outlet yang dipilih' }, { status: 400 });
         }

        // Cek existing transaction by bookingId
        const existingTransaction = await prisma.transaction.findUnique({
             where: { bookingId }
         });

        if (existingTransaction) {
              console.warn(`Transaction for booking ID ${bookingId} already exists.`);
              return NextResponse.json({ error: 'Transaksi untuk booking ini sudah ada' }, { status: 409 }); // 409 Conflict
         }
        console.log(`Booking check passed for ID: ${bookingId}`);
    } else {
        console.log("No valid bookingId provided. Proceeding as walk-in.");
    }
    // --- Akhir Logika terkait Booking ---

    // Hapus pembuatan ID Transaksi dengan UUID
    // const newTransactionId = uuidv4();

    console.log("Creating transaction with commission...");
    // Pindahkan ini ke dalam blok `prisma.$transaction` untuk atomicity
    /*
    const newTransaction = await prisma.transaction.create({
      data: {
        // ... data transaksi lama ...
      }
    });
    console.log("Transaction created with commission, ID:", newTransaction.id);
    */

    // --- Gunakan Transaksi Prisma untuk Atomicity ---
    const transactionResult = await prisma.$transaction(async (tx) => {
      console.log("Inside prisma transaction block...");
      // 1. Buat Transaksi Utama
      const createdTransaction = await tx.transaction.create({
        data: {
          totalAmount,
          paymentStatus: finalPaymentStatus,
          paymentMethod: finalPaymentMethod,
          notes: notes || null,
          booking:        bookingId ? { connect: { id: bookingId } } : undefined,
          createdBy:      { connect: { id: createdById } },
          customer:       { connect: { id: customerId } },
          outlet:         { connect: { id: outletId } },
          therapist:      { connect: { id: therapistId } },
          therapistCommissionEarned: calculatedCommission,
          // Tambahkan data diskon jika ada
          discountType: discountType || null,
          discountValue: discountValue || 0,
          discountAmount: discountAmount || 0,
          // Tambahkan data biaya tambahan jika ada
          additionalCharge: additionalCharge || 0,
          // Tambahkan data split payment jika metode pembayaran adalah SPLIT
          ...(finalPaymentMethod === 'SPLIT' && splitPayment ? {
            splitPayment: {
              create: {
                firstMethod: splitPayment.firstMethod.toUpperCase(),
                secondMethod: splitPayment.secondMethod.toUpperCase(),
                cashAmount: splitPayment.cashAmount || 0,
                changeAmount: splitPayment.changeAmount || 0
              }
            }
          } : {})
        }
      });
      console.log("Main transaction created, ID:", createdTransaction.id);

      // 2. Buat TransactionItem
      // Selalu prioritaskan items dari request, terlepas dari apakah ini transaksi walk-in atau dari booking
      if (items && Array.isArray(items) && items.length > 0) {
        console.log("Processing items to create TransactionItem...");
        console.log("Items from frontend:", JSON.stringify(items, null, 2));
        const serviceIds = items.map(item => item.serviceId).filter(id => id);

        if (serviceIds.length > 0) {
           // Ambil detail layanan (terutama harga) dari DB
           const servicesInDb = await tx.service.findMany({
             where: {
               id: { in: serviceIds },
               outlets: {
                 some: {
                   outletId: outletId
                 }
               }
             },
             select: { id: true, price: true, name: true }
           });
           console.log("Services found in DB:", JSON.stringify(servicesInDb, null, 2));
           const servicePriceMap = new Map(servicesInDb.map(s => [s.id, s.price]));
           const serviceNameMap = new Map(servicesInDb.map(s => [s.id, s.name]));

           const transactionItemsData = items.map(item => {
             const price = servicePriceMap.get(item.serviceId) ?? 0;
             const serviceName = serviceNameMap.get(item.serviceId) ?? 'Unknown Service';
             console.log(`Creating TransactionItem for service ${serviceName} (${item.serviceId}), quantity: ${item.quantity}, price: ${price}`);
             return {
               transactionId: createdTransaction.id,
               serviceId: item.serviceId,
               quantity: item.quantity > 0 ? item.quantity : 1,
               price: price
             };
           }).filter(itemData => itemData.price > 0); // Hanya simpan item yg valid harganya

           if (transactionItemsData.length > 0) {
             console.log("Creating TransactionItems:", JSON.stringify(transactionItemsData, null, 2));
             await tx.transactionItem.createMany({
               data: transactionItemsData,
             });
             console.log(`${transactionItemsData.length} TransactionItems created.`);
           } else {
             console.warn("No valid items found to create TransactionItems after price check.");
             // Jika tidak ada item valid setelah pengecekan harga, gunakan fallback dari booking
             await createTransactionItemsFromBooking();
           }
        } else {
           console.warn("Transaction has items array, but no valid service IDs found.");
           // Jika tidak ada serviceId valid, gunakan fallback dari booking
           await createTransactionItemsFromBooking();
        }
      } else {
        console.warn("No items provided to create TransactionItems.");
        // Jika tidak ada items yang diberikan, gunakan fallback dari booking
        await createTransactionItemsFromBooking();
      }

      // Fungsi helper untuk membuat TransactionItems dari booking (sebagai fallback)
      async function createTransactionItemsFromBooking() {
        // Hanya gunakan booking sebagai fallback jika ada bookingId
        if (bookingId && typeof bookingId === 'string' && bookingId.trim() !== '') {
          console.log(`Using booking services as fallback for booking ID: ${bookingId}...`);

          // Ambil booking services dari booking
          const bookingWithServices = await tx.booking.findUnique({
            where: { id: bookingId },
            include: {
              bookingServices: {
                include: {
                  service: {
                    select: {
                      id: true,
                      price: true
                    }
                  }
                }
              }
            }
          });

          if (bookingWithServices && bookingWithServices.bookingServices.length > 0) {
            // Kelompokkan layanan yang sama dan hitung quantity
            const serviceMap = new Map();

            bookingWithServices.bookingServices.forEach(bs => {
              const serviceId = bs.serviceId;
              if (serviceMap.has(serviceId)) {
                // Jika layanan sudah ada, tambahkan quantity
                const existingItem = serviceMap.get(serviceId);
                existingItem.quantity += 1;
              } else {
                // Jika layanan belum ada, tambahkan dengan quantity 1
                serviceMap.set(serviceId, {
                  transactionId: createdTransaction.id,
                  serviceId: serviceId,
                  quantity: 1,
                  price: bs.price // Gunakan harga yang disimpan di BookingService
                });
              }
            });

            // Konversi Map ke array untuk createMany
            const transactionItemsData = Array.from(serviceMap.values());

            if (transactionItemsData.length > 0) {
              console.log("Creating TransactionItems from booking as fallback:", transactionItemsData);
              await tx.transactionItem.createMany({
                data: transactionItemsData,
              });
              console.log(`${transactionItemsData.length} TransactionItems created from booking as fallback.`);
            } else {
              console.warn("No valid booking services found to create TransactionItems.");
            }
          } else {
            console.warn(`No booking services found for booking ID: ${bookingId}`);
          }
        } else {
          console.warn("No bookingId provided for fallback. No TransactionItems will be created.");
        }
      }

      // 3. Format dan Update displayId (di dalam transaksi)
      if (typeof createdTransaction.id === 'number') {
          const displayId = `TR${String(createdTransaction.id).padStart(7, '0')}`;
          await tx.transaction.update({
              where: { id: createdTransaction.id },
              data: { displayId: displayId },
          });
          console.log(`Updated displayId for transaction ${createdTransaction.id} to ${displayId}`);
          // Tambahkan displayId ke objek yang akan dikembalikan
          (createdTransaction as any).displayId = displayId;
      } else {
         console.warn("Transaction ID is not a number, cannot create displayId.");
      }

      // 4. Update status booking jika perlu (di dalam transaksi)
      if (createdTransaction.paymentStatus === 'PAID' && bookingData && bookingData.status !== 'COMPLETED') {
        console.log(`Updating booking ${bookingId} status to COMPLETED inside transaction.`);
        await tx.booking.update({
          where: { id: bookingId! }, // bookingId pasti ada jika bookingData ada
          data: { status: 'COMPLETED' }
        });
        console.log(`Booking ${bookingId} status updated inside transaction.`);
      }

      console.log("Prisma transaction block finished.");
      return createdTransaction;
    }); // Akhir prisma.$transaction

    console.log("Transaction process completed. Result:", transactionResult);

    // Verifikasi TransactionItems telah dibuat dengan benar
    try {
      const transactionItems = await prisma.transactionItem.findMany({
        where: { transactionId: transactionResult.id },
        include: { service: { select: { name: true } } }
      });

      console.log(`Verification: Found ${transactionItems.length} TransactionItems for transaction ID ${transactionResult.id}:`);
      transactionItems.forEach(item => {
        console.log(`- Service: ${item.service.name}, Quantity: ${item.quantity}, Price: ${item.price}`);
      });

      if (transactionItems.length === 0) {
        console.warn(`WARNING: No TransactionItems found for transaction ID ${transactionResult.id}. This might indicate an issue.`);
      }
    } catch (verifyError) {
      console.error("Error verifying TransactionItems:", verifyError);
    }

    // Log pembuatan transaksi
    await logTransaction(
      'create',
      transactionResult.id,
      {
        totalAmount,
        paymentMethod: finalPaymentMethod,
        paymentStatus: finalPaymentStatus,
        bookingId: bookingId || null,
        customerId,
        therapistId,
        therapistCommissionEarned: calculatedCommission,
        splitPayment: splitPayment || null
      },
      outletId,
      createdById
    );

    return NextResponse.json({
      message: 'Transaksi berhasil dibuat',
      transaction: transactionResult // Kembalikan hasil dari $transaction
    }, { status: 201 });

  } catch (error: unknown) {
    console.error('--- POST /api/transactions --- ERROR ---', error);
    // Periksa tipe error
    if (error instanceof Error && error.name === 'PrismaClientKnownRequestError') {
      const prismaError = error as any;
      if (prismaError.code === 'P2002') { // Unique constraint violation
        return NextResponse.json({ error: 'Constraint unik terlanggar (misal: ID display sudah ada)' }, { status: 409 });
      } else if (prismaError.code === 'P2003') { // Foreign key constraint failed
        return NextResponse.json({ error: 'Referensi ke data lain tidak valid (misal: customerId, outletId tidak ditemukan)' }, { status: 400 });
      }
    }
    const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat membuat transaksi';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}