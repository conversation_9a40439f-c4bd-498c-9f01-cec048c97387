import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logTransaction } from '@/lib/logger';
import { getUserIdFromToken } from '@/lib/auth-utils';

// Handler untuk metode DELETE untuk menghapus transaksi
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Pastikan params sudah diawait
    const params = await context.params;
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID transaksi tidak valid' }, { status: 400 });
    }

    // Cek apakah transaksi ada
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        customer: true,
        therapist: true,
        outlet: true,
        booking: true,
        splitPayment: true
      }
    });

    if (!existingTransaction) {
      return NextResponse.json({ error: 'Transaksi tidak ditemukan' }, { status: 404 });
    }

    // Hapus split payment terlebih dahulu jika ada
    if (existingTransaction.splitPayment) {
      await prisma.splitPayment.delete({
        where: { transactionId: id }
      });
    }

    // Hapus transaction items jika ada
    await prisma.transactionItem.deleteMany({
      where: { transactionId: id }
    });

    // Hapus transaksi
    await prisma.transaction.delete({
      where: { id }
    });

    // Ambil user ID dari token
    const userId = getUserIdFromToken(request);

    // Log penghapusan transaksi
    await logTransaction(
      'delete',
      id,
      {
        customerName: existingTransaction.customer?.name,
        therapistName: existingTransaction.therapist?.name,
        totalAmount: existingTransaction.totalAmount,
        paymentMethod: existingTransaction.paymentMethod,
        bookingId: existingTransaction.booking?.id
      },
      existingTransaction.outletId,
      userId // Gunakan userId dari token
    );

    return NextResponse.json({
      message: 'Transaksi berhasil dihapus',
      transaction: {
        id: existingTransaction.id,
        displayId: existingTransaction.displayId
      }
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus transaksi' },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: Request,
  context: { params: { id: string } }
) {
  try {
    // Pastikan params sudah diawait
    const params = await context.params;
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID transaksi tidak valid' }, { status: 400 });
    }

    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        customer: true,
        therapist: true,
        outlet: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        booking: {
          include: {
            bookingServices: {
              include: {
                service: true,
              },
            },
          },
        },
        transactionItems: {
          include: {
            service: true,
          },
        },
        splitPayment: true, // Include split payment data
      },
    });

    if (!transaction) {
      return NextResponse.json({ error: 'Transaksi tidak ditemukan' }, { status: 404 });
    }

    // Format items untuk response
    const items = transaction.transactionItems.map(item => ({
      id: item.id,
      name: item.service?.name || item.product?.name || 'Item',
      price: item.price,
      quantity: item.quantity
    }));

    // Format response untuk halaman cetak
    // Karena beberapa field mungkin tidak ada di model Transaction, kita hanya sertakan yang ada

    // Hitung subtotal dari items (harga layanan sebelum diskon dan biaya tambahan)
    const itemsTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // Hitung subtotal yang benar
    // Jika ada diskon dan biaya tambahan, subtotal = total - biaya tambahan + diskon
    // Jika tidak ada, gunakan total dari items
    const calculatedSubtotal = transaction.discountAmount > 0 || transaction.additionalCharge > 0
      ? transaction.totalAmount - (transaction.additionalCharge || 0) + (transaction.discountAmount || 0)
      : itemsTotal > 0 ? itemsTotal : transaction.totalAmount;

    const formattedTransaction = {
      id: transaction.id,
      displayId: transaction.displayId || `TR${String(transaction.id).padStart(7, '0')}`,
      transactionDate: transaction.transactionDate,
      createdAt: transaction.createdAt,
      customer: transaction.customer,
      therapist: transaction.therapist,
      outlet: transaction.outlet,
      items: items,
      // Gunakan subtotal yang dihitung
      subtotal: calculatedSubtotal,
      // Selalu sertakan field diskon dan biaya tambahan dengan nilai default jika tidak ada
      discountType: transaction.discountType || 'none',
      discountValue: transaction.discountValue || 0,
      discountAmount: transaction.discountAmount || 0,
      additionalCharge: transaction.additionalCharge || 0,
      tax: transaction.tax || 0,
      // Field yang pasti ada
      totalAmount: transaction.totalAmount,
      paymentMethod: transaction.paymentMethod,
      notes: transaction.notes,
      createdBy: transaction.createdBy,
      splitPayment: transaction.splitPayment
    };

    return NextResponse.json(formattedTransaction);
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data transaksi' },
      { status: 500 }
    );
  }
}